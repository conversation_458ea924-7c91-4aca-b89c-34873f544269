import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { uploadAPI } from './api';
import { CloudinaryUploadResult } from '@/types';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Image validation
export const validateImageFile = (file: File): { isValid: boolean; error?: string } => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Please select a valid image file (JPEG, PNG, or WebP)',
    };
  }

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'Image size must be less than 10MB',
    };
  }

  return { isValid: true };
};

// Upload image to Cloudinary with signature
export const uploadImageToCloudinary = async (file: File): Promise<CloudinaryUploadResult> => {
  try {
    console.log('Starting Cloudinary upload with signature...');

    // Get upload signature from backend
    const signatureResponse = await uploadAPI.getUploadSignature();
    console.log('Signature response:', signatureResponse);

    if (!signatureResponse.success || !signatureResponse.data) {
      console.error('Failed to get upload signature:', signatureResponse);
      throw new Error('Failed to get upload signature');
    }

    const {
      signature,
      timestamp,
      apiKey,
      uploadUrl,
      folder,
      cloudName,
      public_id,
      context,
      tags,
      transformation
    } = signatureResponse.data;

    console.log('Upload parameters:', {
      signature: !!signature,
      timestamp,
      apiKey,
      uploadUrl,
      folder,
      cloudName,
      public_id: !!public_id,
      context: !!context,
      tags: !!tags,
      transformation: !!transformation
    });

    // Create form data for Cloudinary upload
    // Parameters must be in alphabetical order for signature validation
    const formData = new FormData();
    formData.append('file', file);
    formData.append('api_key', apiKey);

    // Add parameters in alphabetical order (as they were signed)
    if (context) {
      formData.append('context', context);
    }
    formData.append('folder', folder);
    if (public_id) {
      formData.append('public_id', public_id);
    }
    formData.append('signature', signature);
    if (tags) {
      formData.append('tags', tags);
    }
    formData.append('timestamp', timestamp.toString());
    if (transformation) {
      formData.append('transformation', transformation);
    }

    console.log('Uploading to Cloudinary...');

    // Upload to Cloudinary
    const uploadResponse = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
    });

    console.log('Upload response status:', uploadResponse.status);

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      console.error('Upload failed:', errorText);
      throw new Error(`Failed to upload image: ${uploadResponse.status}`);
    }

    const result: CloudinaryUploadResult = await uploadResponse.json();
    console.log('Upload successful:', result);
    return result;
  } catch (error) {
    console.error('Upload error:', error);
    throw new Error('Failed to upload image. Please try again.');
  }
};

// Direct upload to Cloudinary (unsigned upload)
export const uploadImageDirectly = async (file: File): Promise<CloudinaryUploadResult> => {
  try {
    console.log('Starting direct Cloudinary upload...');

    // Try multiple common upload presets
    const cloudName = 'dy1tsskkm';
    const uploadUrl = `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`;
    const presets = ['ml_default', 'unsigned', 'face-app', 'default'];

    for (const preset of presets) {
      try {
        console.log(`Trying upload preset: ${preset}`);

        const formData = new FormData();
        formData.append('file', file);
        formData.append('upload_preset', preset);
        formData.append('folder', 'faceapp-uploads');

        const uploadResponse = await fetch(uploadUrl, {
          method: 'POST',
          body: formData,
        });

        console.log(`Upload response for ${preset}:`, uploadResponse.status);

        if (uploadResponse.ok) {
          const result: CloudinaryUploadResult = await uploadResponse.json();
          console.log('Direct upload successful with preset:', preset, result);
          return result;
        } else {
          const errorText = await uploadResponse.text();
          console.log(`Preset ${preset} failed:`, errorText);
        }
      } catch (presetError) {
        console.log(`Preset ${preset} error:`, presetError);
        continue;
      }
    }

    throw new Error('All upload presets failed');
  } catch (error) {
    console.error('Direct upload error:', error);
    throw new Error('Failed to upload image directly. Please try again.');
  }
};

// Upload through backend API (most reliable)
export const uploadImageThroughBackend = async (file: File): Promise<CloudinaryUploadResult> => {
  try {
    console.log('Starting backend upload...');

    const response = await uploadAPI.uploadImage(file);
    console.log('Backend upload response:', response);

    if (response.success && response.data) {
      return {
        secure_url: response.data.imageUrl,
        public_id: response.data.publicId,
        width: 0, // These will be filled by the backend
        height: 0,
        format: 'jpg',
        resource_type: 'image'
      };
    } else {
      throw new Error(response.message || 'Backend upload failed');
    }
  } catch (error) {
    console.error('Backend upload error:', error);
    throw new Error('Failed to upload image through backend. Please try again.');
  }
};

// Format date
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Format confidence percentage
export const formatConfidence = (confidence: number): string => {
  return `${(confidence * 100).toFixed(1)}%`;
};

// Color utilities
export const isLightColor = (hex: string): boolean => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 128;
};

export const getContrastColor = (hex: string): string => {
  return isLightColor(hex) ? '#000000' : '#ffffff';
};

// Debounce function
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Local storage utilities
export const storage = {
  get: <T>(key: string, defaultValue?: T): T | null => {
    if (typeof window === 'undefined') return defaultValue || null;
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue || null;
    } catch {
      return defaultValue || null;
    }
  },

  set: <T>(key: string, value: T): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  },

  remove: (key: string): void => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(key);
  },
};

// Error handling
export const getErrorMessage = (error: any): string => {
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }
  if (error?.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

// Validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Generate random ID
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

// Copy to clipboard
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    const success = document.execCommand('copy');
    document.body.removeChild(textArea);
    return success;
  }
};
