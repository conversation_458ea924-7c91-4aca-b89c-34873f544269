import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { uploadAPI } from './api';
import { CloudinaryUploadResult } from '@/types';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Image validation
export const validateImageFile = (file: File): { isValid: boolean; error?: string } => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Please select a valid image file (JPEG, PNG, or WebP)',
    };
  }

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'Image size must be less than 10MB',
    };
  }

  return { isValid: true };
};

// Main upload function with multiple fallback methods
export const uploadImageToCloudinary = async (file: File): Promise<CloudinaryUploadResult> => {
  console.log('Starting Cloudinary upload process...');

  // Method 1: Try web signature endpoint
  try {
    console.log('Method 1: Trying web signature upload...');
    const result = await uploadWithWebSignature(file);
    console.log('Web signature upload successful');
    return result;
  } catch (webError) {
    console.log('Web signature upload failed:', webError.message);
  }

  // Method 2: Try mobile signature endpoint
  try {
    console.log('Method 2: Trying mobile signature upload...');
    const result = await uploadWithMobileSignature(file);
    console.log('Mobile signature upload successful');
    return result;
  } catch (mobileError) {
    console.log('Mobile signature upload failed:', mobileError.message);
  }

  // Method 3: Try backend upload
  try {
    console.log('Method 3: Trying backend upload...');
    const result = await uploadThroughBackend(file);
    console.log('Backend upload successful');
    return result;
  } catch (backendError) {
    console.log('Backend upload failed:', backendError.message);
  }

  // Method 4: Try simple upload with minimal parameters
  try {
    console.log('Method 4: Trying simple upload...');
    const result = await uploadImageSimple(file);
    console.log('Simple upload successful');
    return result;
  } catch (simpleError) {
    console.log('Simple upload failed:', simpleError.message);
  }

  // Method 5: Try unsigned upload
  try {
    console.log('Method 5: Trying unsigned upload...');
    const result = await uploadUnsigned(file);
    console.log('Unsigned upload successful');
    return result;
  } catch (unsignedError) {
    console.log('Unsigned upload failed:', unsignedError.message);
  }

  throw new Error('All upload methods failed. Please try again or contact support.');
};

// Upload with web signature
const uploadWithWebSignature = async (file: File): Promise<CloudinaryUploadResult> => {
  const signatureResponse = await uploadAPI.getUploadSignature();

  if (!signatureResponse.success || !signatureResponse.data) {
    throw new Error('Failed to get web upload signature');
  }

  return await uploadAPI.uploadToCloudinary(file, signatureResponse.data);
};

// Upload with mobile signature (fallback)
const uploadWithMobileSignature = async (file: File): Promise<CloudinaryUploadResult> => {
  const signatureResponse = await uploadAPI.getMobileUploadSignature();

  if (!signatureResponse.success || !signatureResponse.data) {
    throw new Error('Failed to get mobile upload signature');
  }

  return await uploadAPI.uploadToCloudinary(file, signatureResponse.data);
};

// Upload through backend
const uploadThroughBackend = async (file: File): Promise<CloudinaryUploadResult> => {
  const response = await uploadAPI.uploadImage(file);

  if (!response.success || !response.data) {
    throw new Error('Backend upload failed');
  }

  return {
    secure_url: response.data.imageUrl,
    public_id: response.data.publicId,
    width: 0,
    height: 0,
    format: 'jpg',
    resource_type: 'image'
  };
};

// Unsigned upload method
const uploadUnsigned = async (file: File): Promise<CloudinaryUploadResult> => {
  const cloudName = 'dy1tsskkm';
  const uploadUrl = `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`;

  // Try common unsigned presets
  const presets = ['ml_default', 'unsigned', 'face-app', 'default', 'web_upload'];

  for (const preset of presets) {
    try {
      console.log(`Trying unsigned preset: ${preset}`);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('upload_preset', preset);
      formData.append('folder', 'faceapp-uploads');

      const uploadResponse = await fetch(uploadUrl, {
        method: 'POST',
        body: formData,
      });

      if (uploadResponse.ok) {
        const result: CloudinaryUploadResult = await uploadResponse.json();
        console.log('Unsigned upload successful with preset:', preset);
        return result;
      } else {
        const errorText = await uploadResponse.text();
        console.log(`Preset ${preset} failed:`, errorText);
      }
    } catch (presetError) {
      console.log(`Preset ${preset} error:`, presetError);
      continue;
    }
  }

  throw new Error('All unsigned upload presets failed');
};

// Legacy direct upload function (kept for compatibility)
export const uploadImageDirectly = async (file: File): Promise<CloudinaryUploadResult> => {
  return await uploadUnsigned(file);
};

// Simple Cloudinary upload with minimal parameters
export const uploadImageSimple = async (file: File): Promise<CloudinaryUploadResult> => {
  try {
    console.log('Starting simple Cloudinary upload...');

    // Get basic signature
    const signatureResponse = await uploadAPI.getMobileUploadSignature();

    if (!signatureResponse.success || !signatureResponse.data) {
      throw new Error('Failed to get upload signature');
    }

    const { signature, timestamp, apiKey, cloudName } = signatureResponse.data;
    const uploadUrl = `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`;

    // Create minimal form data
    const formData = new FormData();
    formData.append('file', file);
    formData.append('api_key', apiKey);
    formData.append('timestamp', timestamp.toString());
    formData.append('signature', signature);

    console.log('Uploading with minimal parameters...');

    const uploadResponse = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      console.error('Simple upload failed:', errorText);
      throw new Error(`Upload failed: ${uploadResponse.status}`);
    }

    const result: CloudinaryUploadResult = await uploadResponse.json();
    console.log('Simple upload successful:', result);
    return result;
  } catch (error) {
    console.error('Simple upload error:', error);
    throw new Error('Simple upload failed. Please try again.');
  }
};

// Upload through backend API (most reliable)
export const uploadImageThroughBackend = async (file: File): Promise<CloudinaryUploadResult> => {
  return await uploadThroughBackend(file);
};

// Format date
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Format confidence percentage
export const formatConfidence = (confidence: number): string => {
  return `${(confidence * 100).toFixed(1)}%`;
};

// Color utilities
export const isLightColor = (hex: string): boolean => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 128;
};

export const getContrastColor = (hex: string): string => {
  return isLightColor(hex) ? '#000000' : '#ffffff';
};

// Debounce function
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Local storage utilities
export const storage = {
  get: <T>(key: string, defaultValue?: T): T | null => {
    if (typeof window === 'undefined') return defaultValue || null;
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue || null;
    } catch {
      return defaultValue || null;
    }
  },

  set: <T>(key: string, value: T): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  },

  remove: (key: string): void => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(key);
  },
};

// Error handling
export const getErrorMessage = (error: any): string => {
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }
  if (error?.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

// Validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Generate random ID
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

// Copy to clipboard
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    const success = document.execCommand('copy');
    document.body.removeChild(textArea);
    return success;
  }
};
