// Simple test script to verify the API is working
const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';

async function testAPI() {
  console.log('Testing Face App API...');
  
  // Test 1: Health check
  try {
    console.log('\n1. Testing health check...');
    const healthResponse = await fetch(`${API_BASE_URL}/health`);
    const healthData = await healthResponse.json();
    console.log('Health check response:', healthData);
  } catch (error) {
    console.error('Health check failed:', error.message);
  }
  
  // Test 2: Register a test user
  const testUser = {
    name: 'Test User',
    email: `test${Date.now()}@example.com`,
    password: 'TestPass123',
    gender: 'male'
  };
  
  try {
    console.log('\n2. Testing user registration...');
    const registerResponse = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser)
    });
    
    const registerData = await registerResponse.json();
    console.log('Register response:', registerData);
    
    if (registerData.success) {
      console.log('✅ Registration successful');
      
      // Test 3: Try to login (this will fail because email is not verified, but we can see the response format)
      console.log('\n3. Testing login (will fail due to unverified email, but shows response format)...');
      const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testUser.email,
          password: testUser.password
        })
      });
      
      const loginData = await loginResponse.json();
      console.log('Login response:', loginData);
      console.log('Login response structure:');
      console.log('- success:', loginData.success);
      console.log('- message:', loginData.message);
      console.log('- token:', loginData.token ? 'present' : 'not present');
      console.log('- user:', loginData.user ? 'present' : 'not present');
      console.log('- data.token:', loginData.data?.token ? 'present' : 'not present');
      console.log('- data.user:', loginData.data?.user ? 'present' : 'not present');
      
    } else {
      console.log('❌ Registration failed:', registerData.message);
    }
    
  } catch (error) {
    console.error('Registration test failed:', error.message);
  }
}

// Run the test
testAPI().catch(console.error);
