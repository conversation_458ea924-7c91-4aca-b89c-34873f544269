'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';

export const SimpleLoginTest: React.FC = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);

  const addResult = (step: string, success: boolean, data: any) => {
    setTestResults(prev => [...prev, { step, success, data, timestamp: new Date().toISOString() }]);
  };

  const testCompleteLoginFlow = async () => {
    setIsLoading(true);
    setTestResults([]);

    try {
      // Step 1: Direct API call
      addResult('Starting login test', true, '<NAME_EMAIL> / TestPass123');

      const response = await fetch('https://faceapp-ttwh.onrender.com/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'TestPass123'
        })
      });

      const data = await response.json();
      addResult('API Response', response.ok, data);

      if (data.success && data.token) {
        // Step 2: Store token
        localStorage.setItem('authToken', data.token);
        addResult('Token Storage', true, 'Token stored in localStorage');

        // Step 3: Verify token is stored
        const storedToken = localStorage.getItem('authToken');
        addResult('Token Verification', !!storedToken, { tokenPresent: !!storedToken, tokenLength: storedToken?.length });

        // Step 4: Test API call with token
        const profileResponse = await fetch('https://faceapp-ttwh.onrender.com/api/auth/me', {
          headers: {
            'Authorization': `Bearer ${storedToken}`,
            'Content-Type': 'application/json'
          }
        });

        const profileData = await profileResponse.json();
        addResult('Profile API Test', profileResponse.ok, profileData);

        // Step 5: Simulate redirect
        addResult('Redirect Test', true, 'About to redirect to dashboard...');
        
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);

      } else {
        addResult('Login Failed', false, data);
      }

    } catch (error) {
      addResult('Error', false, { error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const clearToken = () => {
    localStorage.removeItem('authToken');
    addResult('Token Cleared', true, 'Token removed from localStorage');
  };

  return (
    <div className="card max-w-2xl mx-auto">
      <h2 className="text-xl font-bold text-gray-900 mb-6">Simple Login Test</h2>
      
      <div className="space-y-4 mb-6">
        <LoadingButton
          onClick={testCompleteLoginFlow}
          isLoading={isLoading}
          className="w-full"
        >
          Test Complete Login Flow
        </LoadingButton>
        
        <div className="flex space-x-2">
          <button onClick={clearResults} className="btn-secondary flex-1">
            Clear Results
          </button>
          <button onClick={clearToken} className="btn-secondary flex-1">
            Clear Token
          </button>
        </div>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="space-y-3">
          <h3 className="font-medium text-gray-900">Test Results:</h3>
          {testResults.map((result, index) => (
            <div
              key={index}
              className={`p-3 rounded-lg border ${
                result.success
                  ? 'bg-green-50 border-green-200'
                  : 'bg-red-50 border-red-200'
              }`}
            >
              <div className="flex items-start space-x-2">
                {result.success ? (
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600 mt-0.5" />
                )}
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{result.step}</div>
                  <div className="text-xs text-gray-500 mb-2">{result.timestamp}</div>
                  <pre className="text-sm text-gray-700 overflow-auto">
                    {typeof result.data === 'string' 
                      ? result.data 
                      : JSON.stringify(result.data, null, 2)
                    }
                  </pre>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Current State */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-2">Current State</h4>
        <div className="text-sm space-y-1">
          <div>Token in localStorage: {localStorage.getItem('authToken') ? 'Present' : 'None'}</div>
          <div>Current URL: {typeof window !== 'undefined' ? window.location.href : 'N/A'}</div>
        </div>
      </div>
    </div>
  );
};
