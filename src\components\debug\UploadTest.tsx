'use client';

import React, { useState, useRef } from 'react';
import { Upload, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import { uploadAPI } from '@/lib/api';

export const UploadTest: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const addResult = (step: string, success: boolean, data: any) => {
    setTestResults(prev => [...prev, { step, success, data, timestamp: new Date().toISOString() }]);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      addResult('File Selected', true, { name: file.name, size: file.size, type: file.type });
    }
  };

  const testSignatureUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setTestResults([]);

    try {
      // Step 1: Get signature
      addResult('Getting Upload Signature', true, 'Requesting signature from API...');
      
      const signatureResponse = await uploadAPI.getUploadSignature();
      addResult('Signature Response', signatureResponse.success, signatureResponse);

      if (!signatureResponse.success || !signatureResponse.data) {
        addResult('Upload Failed', false, 'No signature data received');
        return;
      }

      const { 
        signature, 
        timestamp, 
        apiKey, 
        uploadUrl, 
        folder,
        public_id,
        context,
        tags,
        transformation
      } = signatureResponse.data;

      // Step 2: Upload to Cloudinary
      addResult('Uploading to Cloudinary', true, 'Preparing form data...');

      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('api_key', apiKey);
      
      if (context) formData.append('context', context);
      formData.append('folder', folder);
      if (public_id) formData.append('public_id', public_id);
      formData.append('signature', signature);
      if (tags) formData.append('tags', tags);
      formData.append('timestamp', timestamp.toString());
      if (transformation) formData.append('transformation', transformation);

      const uploadResponse = await fetch(uploadUrl, {
        method: 'POST',
        body: formData,
      });

      const uploadResult = await uploadResponse.json();
      addResult('Cloudinary Upload', uploadResponse.ok, {
        status: uploadResponse.status,
        result: uploadResult
      });

      if (uploadResponse.ok && uploadResult.secure_url) {
        addResult('Upload Success', true, {
          imageUrl: uploadResult.secure_url,
          publicId: uploadResult.public_id
        });
      }

    } catch (error) {
      addResult('Upload Error', false, { error: error.message });
    } finally {
      setIsUploading(false);
    }
  };

  const testDirectUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setTestResults([]);

    try {
      addResult('Direct Upload Test', true, 'Testing direct upload to Cloudinary...');

      // Try different upload presets that might exist
      const presets = ['ml_default', 'unsigned', 'face-app'];
      
      for (const preset of presets) {
        try {
          const formData = new FormData();
          formData.append('file', selectedFile);
          formData.append('upload_preset', preset);
          formData.append('folder', 'faceapp-uploads');

          const uploadUrl = 'https://api.cloudinary.com/v1_1/dy1tsskkm/image/upload';
          
          const uploadResponse = await fetch(uploadUrl, {
            method: 'POST',
            body: formData,
          });

          const uploadResult = await uploadResponse.json();
          
          if (uploadResponse.ok) {
            addResult(`Direct Upload Success (${preset})`, true, {
              preset,
              imageUrl: uploadResult.secure_url,
              publicId: uploadResult.public_id
            });
            return;
          } else {
            addResult(`Direct Upload Failed (${preset})`, false, {
              preset,
              status: uploadResponse.status,
              error: uploadResult
            });
          }
        } catch (error) {
          addResult(`Direct Upload Error (${preset})`, false, { preset, error: error.message });
        }
      }

    } catch (error) {
      addResult('Direct Upload Error', false, { error: error.message });
    } finally {
      setIsUploading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="card max-w-2xl mx-auto">
      <h2 className="text-xl font-bold text-gray-900 mb-6">Upload Test</h2>
      
      {/* File Selection */}
      <div className="mb-6">
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        <button
          onClick={() => fileInputRef.current?.click()}
          className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 transition-colors"
        >
          <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
          <p className="text-gray-600">
            {selectedFile ? selectedFile.name : 'Click to select an image'}
          </p>
        </button>
      </div>

      {/* Test Buttons */}
      {selectedFile && (
        <div className="space-y-4 mb-6">
          <LoadingButton
            onClick={testSignatureUpload}
            isLoading={isUploading}
            className="w-full"
          >
            Test Signature Upload
          </LoadingButton>
          
          <LoadingButton
            onClick={testDirectUpload}
            isLoading={isUploading}
            className="w-full"
          >
            Test Direct Upload
          </LoadingButton>
          
          <button onClick={clearResults} className="w-full btn-secondary">
            Clear Results
          </button>
        </div>
      )}

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="space-y-3">
          <h3 className="font-medium text-gray-900">Test Results:</h3>
          {testResults.map((result, index) => (
            <div
              key={index}
              className={`p-3 rounded-lg border ${
                result.success
                  ? 'bg-green-50 border-green-200'
                  : 'bg-red-50 border-red-200'
              }`}
            >
              <div className="flex items-start space-x-2">
                {result.success ? (
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600 mt-0.5" />
                )}
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{result.step}</div>
                  <div className="text-xs text-gray-500 mb-2">{result.timestamp}</div>
                  <pre className="text-sm text-gray-700 overflow-auto max-h-40">
                    {typeof result.data === 'string' 
                      ? result.data 
                      : JSON.stringify(result.data, null, 2)
                    }
                  </pre>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
