// User types
export interface User {
  id: string;
  name: string;
  email: string;
  isEmailVerified: boolean;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
}

// Auth types
export interface RegisterData {
  name: string;
  email: string;
  password: string;
  gender: 'male' | 'female' | 'other' | 'prefer_not_to_say';
}

export interface LoginData {
  email: string;
  password: string;
}

export interface OTPData {
  email: string;
  otp: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: Array<{
    msg: string;
    param: string;
  }>;
  retryAfter?: number;
}

export interface AuthResponse {
  token: string;
  user: User;
}

// Login API response (matches actual API format)
export interface LoginResponse {
  success: boolean;
  message: string;
  token?: string;
  user?: User;
  data?: {
    token: string;
    user: User;
  };
}

// Face Analysis types (updated to match API response)
export interface FaceAnalysis {
  _id: string;
  imageUrl: string;
  originalFileName: string;
  faceDetected: boolean;
  colors: {
    hairColor: {
      primary: string;
      hex: string;
      rgb: { r: number; g: number; b: number };
      confidence: number;
    };
    skinTone: {
      primary: string;
      hex: string;
      rgb: { r: number; g: number; b: number };
      confidence: number;
    };
    eyeColor: {
      primary: string;
      hex: string;
      rgb: { r: number; g: number; b: number };
      confidence: number;
    };
    lipColor: {
      primary: string;
      hex: string;
      rgb: { r: number; g: number; b: number };
      confidence: number;
    };
  };
  faceDimensions: {
    faceLength: number;
    faceWidth: number;
    jawWidth: number;
    foreheadWidth: number;
    cheekboneWidth: number;
    lengthToWidthRatio: number;
    jawToForeheadRatio: number;
    cheekboneToJawRatio: number;
  };
  facialFeatures: {
    faceShape: string;
    eyeShape: string;
    eyeDistance: string;
    eyebrowShape: string;
    noseShape: string;
    lipShape: string;
  };
  analysisMetadata: {
    processingTime: number;
    confidence: number;
    algorithm: string;
    errors: string[];
    warnings: string[];
  };
  createdAt: string;
}

export interface AnalysisRequest {
  imageUrl: string;
  publicId: string;
}

// Color Recommendation types (updated to match API response)
export interface OutfitRecommendation {
  outfitName: string;
  shirt: {
    color: string;
    hex: string;
    reason: string;
  };
  pants: {
    color: string;
    hex: string;
    reason: string;
  };
  shoes?: {
    color: string;
    hex: string;
    reason: string;
  };
  overallReason: string;
}

export interface ColorRecommendation {
  recommendationId: string;
  faceAnalysisId: string;
  aiService: string;
  processingTime: number;
  cached: boolean;
  outfits: OutfitRecommendation[];
  colorPalette: {
    bestColors: string[];
    avoidColors: string[];
    seasonalType: string;
  };
  advice: string;
  confidence: number;
  createdAt: string;
}

export interface RecommendationRequest {
  preferences: {
    style?: string;
    occasion?: string;
  };
}

// Upload types
export interface UploadSignature {
  signature: string;
  timestamp: number;
  cloudName: string;
  apiKey: string;
  uploadUrl: string;
  folder?: string;
  public_id?: string;
  context?: string;
  tags?: string;
  transformation?: string;
  allowed_formats?: string;
  formData?: {
    [key: string]: any;
  };
}

export interface CloudinaryUploadResult {
  secure_url: string;
  public_id: string;
  width: number;
  height: number;
  format: string;
  resource_type: string;
}

// Feedback types
export interface FeedbackData {
  rating: number;
  feedback: string;
  favoriteOutfits: number[];
}

// Loading states
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

// Error types
export interface ErrorState {
  hasError: boolean;
  message?: string;
  code?: string;
}
