'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Eye, EyeOff, RefreshCw } from 'lucide-react';

export const AuthStatus: React.FC = () => {
  const { user, isAuthenticated, loading, refreshUser } = useAuth();
  const [showToken, setShowToken] = useState(false);
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    const updateToken = () => {
      setToken(localStorage.getItem('authToken'));
    };
    
    updateToken();
    
    // Listen for storage changes
    window.addEventListener('storage', updateToken);
    
    // Also check periodically in case of programmatic changes
    const interval = setInterval(updateToken, 1000);
    
    return () => {
      window.removeEventListener('storage', updateToken);
      clearInterval(interval);
    };
  }, []);

  const handleRefresh = async () => {
    await refreshUser();
    setToken(localStorage.getItem('authToken'));
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-medium text-gray-900">Auth Status</h3>
        <button
          onClick={handleRefresh}
          className="p-1 text-gray-500 hover:text-gray-700"
          title="Refresh"
        >
          <RefreshCw className="w-4 h-4" />
        </button>
      </div>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Authenticated:</span>
          <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>
            {isAuthenticated ? 'Yes' : 'No'}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Loading:</span>
          <span className={loading.isLoading ? 'text-yellow-600' : 'text-gray-900'}>
            {loading.isLoading ? 'Yes' : 'No'}
          </span>
        </div>
        
        {loading.message && (
          <div className="text-xs text-gray-500">
            {loading.message}
          </div>
        )}
        
        <div className="flex justify-between">
          <span className="text-gray-600">User:</span>
          <span className="text-gray-900">
            {user ? user.name : 'None'}
          </span>
        </div>
        
        {user && (
          <div className="text-xs text-gray-500">
            {user.email}
          </div>
        )}
        
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Token:</span>
          <div className="flex items-center space-x-1">
            <span className={token ? 'text-green-600' : 'text-red-600'}>
              {token ? 'Present' : 'None'}
            </span>
            {token && (
              <button
                onClick={() => setShowToken(!showToken)}
                className="p-1 text-gray-400 hover:text-gray-600"
              >
                {showToken ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
              </button>
            )}
          </div>
        </div>
        
        {token && showToken && (
          <div className="text-xs text-gray-500 break-all">
            {token.substring(0, 50)}...
          </div>
        )}
      </div>
    </div>
  );
};
