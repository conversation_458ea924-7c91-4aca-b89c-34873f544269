// Create a test user and verify email for testing
const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';

const testUser = {
  name: 'Test User',
  email: '<EMAIL>',
  password: 'TestPass123',
  gender: 'male'
};

async function createAndVerifyTestUser() {
  console.log('Creating test user for login testing...');
  
  try {
    // Step 1: Register
    console.log('\n1. Registering user...');
    const registerResponse = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser)
    });
    
    const registerData = await registerResponse.json();
    console.log('Register response:', registerData);
    
    if (!registerData.success) {
      console.log('Registration failed (user might already exist):', registerData.message);
      
      // Try to login directly to see if user exists and is verified
      console.log('\n2. Trying to login with existing user...');
      const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testUser.email,
          password: testUser.password
        })
      });
      
      const loginData = await loginResponse.json();
      console.log('Login response:', loginData);
      
      if (loginData.success) {
        console.log('✅ Test user exists and can login successfully!');
        console.log('User details:', loginData.user);
        console.log('Token received:', loginData.token ? 'Yes' : 'No');
      } else {
        console.log('❌ Login failed:', loginData.message);
      }
      
      return;
    }
    
    // Step 2: Try to verify with a common OTP (many test systems use 123456)
    console.log('\n2. Attempting to verify email with common test OTP...');
    const otpResponse = await fetch(`${API_BASE_URL}/auth/verify-email-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
        otp: '123456'
      })
    });
    
    const otpData = await otpResponse.json();
    console.log('OTP verification response:', otpData);
    
    // Step 3: Try to login
    console.log('\n3. Attempting login...');
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('Login response:', loginData);
    
    if (loginData.success) {
      console.log('✅ Test user created and can login successfully!');
      console.log('User details:', loginData.user);
      console.log('Token received:', loginData.token ? 'Yes' : 'No');
    } else {
      console.log('❌ Login failed:', loginData.message);
      console.log('Note: You may need to check your email for the OTP and verify manually');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

createAndVerifyTestUser().catch(console.error);
