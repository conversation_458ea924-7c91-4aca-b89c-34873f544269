// Test all upload methods
const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';

async function testAllUploadMethods() {
  console.log('Testing All Upload Methods...');
  
  // Login first
  try {
    console.log('\n1. Logging in...');
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'TestPass123'
      })
    });
    
    const loginData = await loginResponse.json();
    
    if (!loginData.success || !loginData.token) {
      console.error('❌ Login failed');
      return;
    }
    
    const token = loginData.token;
    console.log('✅ Login successful');
    
    // Test Method 1: Web signature endpoint
    console.log('\n2. Testing web signature endpoint...');
    try {
      const webSignatureResponse = await fetch(`${API_BASE_URL}/upload/signature`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('Web signature status:', webSignatureResponse.status);
      const webSignatureData = await webSignatureResponse.json();
      console.log('Web signature response:', webSignatureData);
      
      if (webSignatureData.success) {
        console.log('✅ Web signature endpoint working');
      } else {
        console.log('❌ Web signature endpoint failed');
      }
    } catch (webError) {
      console.log('❌ Web signature endpoint not available:', webError.message);
    }
    
    // Test Method 2: Mobile signature endpoint
    console.log('\n3. Testing mobile signature endpoint...');
    try {
      const mobileSignatureResponse = await fetch(`${API_BASE_URL}/upload/mobile-signature`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('Mobile signature status:', mobileSignatureResponse.status);
      const mobileSignatureData = await mobileSignatureResponse.json();
      console.log('Mobile signature response:', mobileSignatureData);
      
      if (mobileSignatureData.success && mobileSignatureData.signature) {
        console.log('✅ Mobile signature endpoint working');
        
        // Test simple upload with minimal parameters
        console.log('\n4. Testing simple Cloudinary upload...');
        const { signature, timestamp, api_key, cloud_name } = mobileSignatureData;
        const uploadUrl = `https://api.cloudinary.com/v1_1/${cloud_name}/image/upload`;
        
        const formData = new FormData();
        formData.append('api_key', api_key);
        formData.append('timestamp', timestamp.toString());
        formData.append('signature', signature);
        // Note: We're not adding a file here, just testing the signature validation
        
        try {
          const uploadResponse = await fetch(uploadUrl, {
            method: 'POST',
            body: formData,
          });
          
          console.log('Simple upload test status:', uploadResponse.status);
          const uploadResult = await uploadResponse.text();
          console.log('Simple upload test response:', uploadResult);
          
          if (uploadResponse.status === 400) {
            const errorData = JSON.parse(uploadResult);
            if (errorData.error && errorData.error.message.includes('Missing required parameter - file')) {
              console.log('✅ Signature validation passed (400 expected without file)');
            } else {
              console.log('⚠️  Signature issue:', errorData.error.message);
            }
          }
        } catch (uploadError) {
          console.error('❌ Simple upload test failed:', uploadError.message);
        }
      } else {
        console.log('❌ Mobile signature endpoint failed');
      }
    } catch (mobileError) {
      console.log('❌ Mobile signature endpoint error:', mobileError.message);
    }
    
    // Test Method 3: Backend upload endpoint
    console.log('\n5. Testing backend upload endpoint...');
    try {
      const backendUploadResponse = await fetch(`${API_BASE_URL}/upload/image`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: new FormData() // Empty form data
      });
      
      console.log('Backend upload status:', backendUploadResponse.status);
      const backendResult = await backendUploadResponse.text();
      console.log('Backend upload response:', backendResult);
      
      if (backendUploadResponse.status === 400) {
        console.log('✅ Backend upload endpoint exists (400 expected without file)');
      } else if (backendUploadResponse.status === 404) {
        console.log('❌ Backend upload endpoint not available');
      }
    } catch (backendError) {
      console.log('❌ Backend upload endpoint error:', backendError.message);
    }
    
    // Test Method 4: Unsigned upload
    console.log('\n6. Testing unsigned upload presets...');
    const cloudName = 'dy1tsskkm';
    const uploadUrl = `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`;
    const presets = ['ml_default', 'unsigned', 'face-app', 'default', 'web_upload'];
    
    for (const preset of presets) {
      try {
        const formData = new FormData();
        formData.append('upload_preset', preset);
        formData.append('folder', 'faceapp-uploads');
        // Note: Not adding file, just testing preset existence
        
        const uploadResponse = await fetch(uploadUrl, {
          method: 'POST',
          body: formData,
        });
        
        console.log(`Preset ${preset} status:`, uploadResponse.status);
        const uploadResult = await uploadResponse.text();
        
        if (uploadResponse.status === 400) {
          const errorData = JSON.parse(uploadResult);
          if (errorData.error && errorData.error.message.includes('Missing required parameter - file')) {
            console.log(`✅ Preset ${preset} exists and working`);
            break; // Found a working preset
          } else if (errorData.error && errorData.error.message.includes('Upload preset not found')) {
            console.log(`❌ Preset ${preset} not found`);
          } else {
            console.log(`⚠️  Preset ${preset} issue:`, errorData.error.message);
          }
        }
      } catch (presetError) {
        console.log(`❌ Preset ${preset} error:`, presetError.message);
      }
    }
    
    // Summary
    console.log('\n📋 Upload Methods Summary:');
    console.log('- Web Signature: Test above');
    console.log('- Mobile Signature: Test above');
    console.log('- Backend Upload: Test above');
    console.log('- Unsigned Upload: Test above');
    console.log('\nNext: Test with actual image file in browser');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

testAllUploadMethods().catch(console.error);
