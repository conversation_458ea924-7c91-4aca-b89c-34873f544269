// Test upload signature API
const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';

async function testUploadSignature() {
  console.log('Testing Upload Signature API...');
  
  // First, login to get a token
  try {
    console.log('\n1. Logging in to get auth token...');
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'TestPass123'
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('Login response:', loginData);
    
    if (!loginData.success || !loginData.token) {
      console.error('❌ Login failed, cannot test upload signature');
      return;
    }
    
    const token = loginData.token;
    console.log('✅ Login successful, token obtained');
    
    // Test upload signature endpoint
    console.log('\n2. Testing upload signature endpoint...');
    const signatureResponse = await fetch(`${API_BASE_URL}/upload/mobile-signature`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('Signature response status:', signatureResponse.status);
    console.log('Signature response headers:', Object.fromEntries(signatureResponse.headers.entries()));
    
    const signatureData = await signatureResponse.json();
    console.log('Signature response data:', signatureData);
    
    if (signatureData.success && signatureData.data) {
      console.log('✅ Upload signature obtained successfully');
      console.log('Signature details:');
      console.log('- Cloud Name:', signatureData.data.cloudName);
      console.log('- API Key:', signatureData.data.apiKey);
      console.log('- Upload URL:', signatureData.data.uploadUrl);
      console.log('- Folder:', signatureData.data.folder);
      console.log('- Signature present:', !!signatureData.data.signature);
      console.log('- Timestamp:', signatureData.data.timestamp);
    } else {
      console.error('❌ Failed to get upload signature:', signatureData);
    }
    
    // Test upload config endpoint
    console.log('\n3. Testing upload config endpoint...');
    const configResponse = await fetch(`${API_BASE_URL}/upload/config`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const configData = await configResponse.json();
    console.log('Config response:', configData);
    
  } catch (error) {
    console.error('Error testing upload:', error);
  }
}

testUploadSignature().catch(console.error);
