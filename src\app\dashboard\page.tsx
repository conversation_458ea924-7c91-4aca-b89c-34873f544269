'use client';

import React, { useState } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { ImageUpload } from '@/components/face/ImageUpload';
import { AnalysisResults } from '@/components/face/AnalysisResults';
import { ColorRecommendations } from '@/components/face/ColorRecommendations';
import { AnalysisHistory } from '@/components/history/AnalysisHistory';
import { UserProfile } from '@/components/profile/UserProfile';
import { ApiTest } from '@/components/debug/ApiTest';
import { AuthTest } from '@/components/debug/AuthTest';
import { AuthStatus } from '@/components/debug/AuthStatus';
import { UploadTest } from '@/components/debug/UploadTest';
import { faceAPI, recommendationAPI } from '@/lib/api';
import { FaceAnalysis, ColorRecommendation, FeedbackData } from '@/types';
import {
  User,
  LogOut,
  History,
  Camera,
  Menu,
  X,
  Settings,
  Home,
  Box
} from 'lucide-react';
import toast from 'react-hot-toast';

type DashboardView = 'upload' | 'analysis' | 'recommendations' | 'history' | 'profile' | 'debug';

export default function DashboardPage() {
  const { user, logout } = useAuth();
  const [currentView, setCurrentView] = useState<DashboardView>('upload');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(false);
  const [isSubmittingFeedback, setIsSubmittingFeedback] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<FaceAnalysis | null>(null);
  const [currentRecommendation, setCurrentRecommendation] = useState<ColorRecommendation | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleImageUpload = async (imageUrl: string, publicId: string) => {
    try {
      setIsAnalyzing(true);
      console.log('Dashboard: Starting face analysis for:', imageUrl);

      // Try the new URL-based analysis first
      let response;
      try {
        response = await faceAPI.analyzeFaceFromUrl(imageUrl, 'uploaded-image.jpg');
        console.log('Dashboard: URL-based analysis response:', response);
      } catch (urlError) {
        console.log('Dashboard: URL-based analysis failed, trying direct method...', urlError);
        // Fallback to direct analysis
        response = await faceAPI.analyzeFace({ imageUrl, publicId });
        console.log('Dashboard: Direct analysis response:', response);
      }

      if (response.success && response.data) {
        // Handle both response formats
        const analysis = (response.data as any).analysis || response.data;
        console.log('Dashboard: Analysis successful:', analysis);
        setCurrentAnalysis(analysis as FaceAnalysis);
        setCurrentView('analysis');
        toast.success('Face analysis completed!');
      } else {
        console.error('Dashboard: Analysis failed:', response);
        toast.error(response.message || 'Analysis failed');
      }
    } catch (error: any) {
      console.error('Dashboard: Analysis error:', error);
      toast.error(error.response?.data?.message || error.message || 'Analysis failed');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleGetRecommendations = async () => {
    if (!currentAnalysis) return;

    try {
      setIsLoadingRecommendations(true);
      const response = await recommendationAPI.getRecommendations(
        currentAnalysis._id,
        { style: 'professional', occasion: 'business' }
      );
      
      if (response.success && response.data) {
        setCurrentRecommendation(response.data);
        setCurrentView('recommendations');
        toast.success('Recommendations generated!');
      } else {
        toast.error(response.message || 'Failed to get recommendations');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to get recommendations');
    } finally {
      setIsLoadingRecommendations(false);
    }
  };

  const handleFeedback = async (feedback: FeedbackData) => {
    if (!currentRecommendation) return;

    try {
      setIsSubmittingFeedback(true);
      // Note: We need the recommendation ID from the API response
      // For now, we'll use a placeholder since the API structure might need adjustment
      await recommendationAPI.addFeedback('recommendation-id', feedback);
      toast.success('Thank you for your feedback!');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to submit feedback');
    } finally {
      setIsSubmittingFeedback(false);
    }
  };

  const handleStartOver = () => {
    setCurrentView('upload');
    setCurrentAnalysis(null);
    setCurrentRecommendation(null);
  };

  const handleSelectAnalysis = (analysis: FaceAnalysis) => {
    setCurrentAnalysis(analysis);
    setCurrentView('analysis');
  };

  const handleLogout = async () => {
    await logout();
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Mobile sidebar backdrop */}
        {sidebarOpen && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Sidebar */}
        <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}>
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
            <h1 className="text-xl font-bold text-gray-900">Face App</h1>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden text-gray-500 hover:text-gray-700"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
          
          <nav className="mt-6 px-6">
            <div className="space-y-2">
              <button
                onClick={() => setCurrentView('upload')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors duration-200 ${
                  currentView === 'upload'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Home className="w-5 h-5" />
                <span>Dashboard</span>
              </button>

              <button
                onClick={handleStartOver}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors duration-200 ${
                  currentView === 'upload'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Camera className="w-5 h-5" />
                <span>New Analysis</span>
              </button>

              <button
                onClick={() => setCurrentView('history')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors duration-200 ${
                  currentView === 'history'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <History className="w-5 h-5" />
                <span>History</span>
              </button>

              <button
                onClick={() => setCurrentView('profile')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors duration-200 ${
                  currentView === 'profile'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Settings className="w-5 h-5" />
                <span>Profile</span>
              </button>

              <button
                onClick={() => setCurrentView('debug')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors duration-200 ${
                  currentView === 'debug'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Settings className="w-5 h-5" />
                <span>Debug Upload</span>
              </button>

              <a
                href="/model-viewer"
                className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors duration-200 text-gray-700 hover:bg-gray-100"
              >
                <Box className="w-5 h-5" />
                <span>3D Model Viewer</span>
              </a>
            </div>
          </nav>

          {/* User Profile */}
          <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-gray-200">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">{user?.name}</p>
                <p className="text-sm text-gray-600">{user?.email}</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="w-full flex items-center space-x-2 px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
            >
              <LogOut className="w-4 h-4" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:ml-64">
          {/* Header */}
          <header className="bg-white shadow-sm border-b border-gray-200">
            <div className="flex items-center justify-between h-16 px-6">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-gray-500 hover:text-gray-700"
              >
                <Menu className="w-6 h-6" />
              </button>
              
              <div className="flex items-center space-x-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  {currentView === 'upload' && 'Dashboard'}
                  {currentView === 'analysis' && 'Analysis Results'}
                  {currentView === 'recommendations' && 'Color Recommendations'}
                  {currentView === 'history' && 'Analysis History'}
                  {currentView === 'profile' && 'Profile Settings'}
                </h2>
              </div>

              {(currentView === 'analysis' || currentView === 'recommendations') && (
                <button
                  onClick={handleStartOver}
                  className="btn-secondary"
                >
                  Start New Analysis
                </button>
              )}
            </div>
          </header>

          {/* Content */}
          <main className="p-6">
            <div className="max-w-4xl mx-auto">
              {currentView === 'upload' && (
                <div className="space-y-8">
                  <ImageUpload
                    onUploadSuccess={handleImageUpload}
                    isAnalyzing={isAnalyzing}
                  />
                  <ApiTest />
                  <AuthTest />
                </div>
              )}

              {currentView === 'analysis' && currentAnalysis && (
                <AnalysisResults
                  analysis={currentAnalysis}
                  onGetRecommendations={handleGetRecommendations}
                  isLoadingRecommendations={isLoadingRecommendations}
                />
              )}

              {currentView === 'recommendations' && currentRecommendation && (
                <ColorRecommendations
                  recommendation={currentRecommendation}
                  onFeedback={handleFeedback}
                  isSubmittingFeedback={isSubmittingFeedback}
                />
              )}

              {currentView === 'history' && (
                <AnalysisHistory onSelectAnalysis={handleSelectAnalysis} />
              )}

              {currentView === 'profile' && (
                <UserProfile />
              )}

              {currentView === 'debug' && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold text-gray-900">Upload Debug</h2>
                  <UploadTest />
                </div>
              )}
            </div>
          </main>
        </div>
      </div>

      {/* Debug Auth Status */}
      <AuthStatus />
    </ProtectedRoute>
  );
}
