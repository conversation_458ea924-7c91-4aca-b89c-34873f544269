import axios, { AxiosResponse, AxiosError } from 'axios';
import {
  ApiResponse,
  AuthResponse,
  LoginResponse,
  RegisterData,
  LoginData,
  OTPData,
  User,
  FaceAnalysis,
  AnalysisRequest,
  ColorRecommendation,
  RecommendationRequest,
  UploadSignature,
  FeedbackData,
} from '@/types';

const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log('API Response:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  (error: AxiosError<ApiResponse>) => {
    console.error('API Error:', {
      url: error.config?.url,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });

    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Auth API functions
export const authAPI = {
  register: async (data: RegisterData): Promise<ApiResponse<{ user: User }>> => {
    const response = await api.post('/auth/register', data);
    return response.data;
  },

  verifyOTP: async (data: OTPData): Promise<ApiResponse> => {
    const response = await api.post('/auth/verify-email-otp', data);
    return response.data;
  },

  login: async (data: LoginData): Promise<LoginResponse> => {
    console.log('API: Sending login request with data:', { email: data.email, password: '***' });
    const response = await api.post('/auth/login', data);
    console.log('API: Raw login response:', response.data);

    const responseData = response.data;

    // Based on our test, the API returns token and user directly in the response
    if (responseData.success && responseData.token && responseData.user) {
      console.log('API: Login successful - token and user found directly in response');
      // Return the response as-is but also add to data property for compatibility
      return {
        success: responseData.success,
        message: responseData.message || 'Login successful',
        token: responseData.token,  // Keep direct access
        user: responseData.user,    // Keep direct access
        data: {
          token: responseData.token,
          user: responseData.user
        }
      };
    }

    // Fallback: check if data is nested (shouldn't happen based on our test)
    if (responseData.success && responseData.data?.token && responseData.data?.user) {
      console.log('API: Login successful - token and user found in nested data');
      return responseData;
    }

    // If login failed
    console.error('API: Login failed:', responseData);
    return {
      success: false,
      message: responseData?.message || 'Login failed',
      data: undefined
    };
  },

  getProfile: async (): Promise<ApiResponse<User>> => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  logout: async (): Promise<ApiResponse> => {
    const response = await api.post('/auth/logout');
    return response.data;
  },

  resendOTP: async (email: string): Promise<ApiResponse> => {
    const response = await api.post('/auth/resend-verification', { email });
    return response.data;
  },
};

// Face Analysis API functions
export const faceAPI = {
  analyzeFace: async (data: AnalysisRequest): Promise<ApiResponse<{ analysis: FaceAnalysis }>> => {
    const response = await api.post('/face/analyze-direct', data);
    return response.data;
  },

  getHistory: async (page = 1, limit = 10): Promise<ApiResponse<{ analyses: FaceAnalysis[] }>> => {
    const response = await api.get(`/face/history?page=${page}&limit=${limit}`);
    return response.data;
  },

  getAnalysis: async (id: string): Promise<ApiResponse<FaceAnalysis>> => {
    const response = await api.get(`/face/analysis/${id}`);
    return response.data;
  },

  deleteAnalysis: async (id: string): Promise<ApiResponse> => {
    const response = await api.delete(`/face/analysis/${id}`);
    return response.data;
  },
};

// Color Recommendation API functions
export const recommendationAPI = {
  getRecommendations: async (
    analysisId: string,
    preferences: RecommendationRequest['preferences']
  ): Promise<ApiResponse<ColorRecommendation>> => {
    const response = await api.post(`/face/analysis/${analysisId}/recommendations`, { preferences });
    return response.data;
  },

  getLatestRecommendation: async (): Promise<ApiResponse<ColorRecommendation>> => {
    const response = await api.get('/face/recommendations/latest');
    return response.data;
  },

  getRecommendationHistory: async (limit = 10): Promise<ApiResponse<ColorRecommendation[]>> => {
    const response = await api.get(`/face/recommendations/history?limit=${limit}`);
    return response.data;
  },

  addFeedback: async (
    recommendationId: string,
    feedback: FeedbackData
  ): Promise<ApiResponse> => {
    const response = await api.post(`/face/recommendations/${recommendationId}/feedback`, feedback);
    return response.data;
  },
};

// Upload API functions
export const uploadAPI = {
  getUploadSignature: async (): Promise<ApiResponse<UploadSignature>> => {
    const response = await api.post('/upload/mobile-signature');
    return response.data;
  },

  getUploadConfig: async (): Promise<ApiResponse> => {
    const response = await api.get('/upload/config');
    return response.data;
  },
};

// Health check
export const healthAPI = {
  check: async (): Promise<ApiResponse> => {
    const response = await axios.get(`${API_BASE_URL}/health`);
    return response.data;
  },

  ping: async (): Promise<string> => {
    const response = await axios.get(`${API_BASE_URL}/ping`);
    return response.data;
  },
};

export default api;
