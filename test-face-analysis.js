// Test face analysis and color recommendations
const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';

async function testFaceAnalysisFlow() {
  console.log('Testing Complete Face Analysis Flow...');
  
  // Login first
  try {
    console.log('\n1. Logging in...');
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'TestPass123'
      })
    });
    
    const loginData = await loginResponse.json();
    
    if (!loginData.success || !loginData.token) {
      console.error('❌ Login failed');
      return;
    }
    
    const token = loginData.token;
    console.log('✅ Login successful');
    
    // Test face analysis from URL
    console.log('\n2. Testing face analysis from URL...');
    const testImageUrl = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face';
    
    try {
      const analysisResponse = await fetch(`${API_BASE_URL}/face/analyze-url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          imageUrl: testImageUrl,
          originalFileName: 'test-face.jpg'
        })
      });
      
      console.log('Analysis response status:', analysisResponse.status);
      const analysisData = await analysisResponse.json();
      console.log('Analysis response:', analysisData);
      
      if (analysisData.success && analysisData.data) {
        console.log('✅ Face analysis successful!');
        const analysis = analysisData.data;
        console.log('Analysis ID:', analysis._id);
        console.log('Face detected:', analysis.faceDetected);
        console.log('Confidence:', analysis.confidence);
        console.log('Colors:', analysis.colors);
        console.log('Facial features:', analysis.facialFeatures);
        
        // Test color recommendations
        console.log('\n3. Testing color recommendations...');
        const recommendationsResponse = await fetch(`${API_BASE_URL}/face/analysis/${analysis._id}/recommendations`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            preferences: {
              style: 'casual',
              occasion: 'everyday',
              season: 'spring'
            }
          })
        });
        
        console.log('Recommendations response status:', recommendationsResponse.status);
        const recommendationsData = await recommendationsResponse.json();
        console.log('Recommendations response:', recommendationsData);
        
        if (recommendationsData.success && recommendationsData.data) {
          console.log('✅ Color recommendations successful!');
          const recommendations = recommendationsData.data;
          console.log('AI Service:', recommendations.aiService);
          console.log('Processing time:', recommendations.processingTime, 'ms');
          console.log('Outfits:', recommendations.outfits?.length || 0);
          console.log('Color palette:', recommendations.colorPalette);
          console.log('General advice:', recommendations.advice?.general);
          
          if (recommendations.outfits && recommendations.outfits.length > 0) {
            console.log('\n🎨 First outfit recommendation:');
            const firstOutfit = recommendations.outfits[0];
            console.log('- Name:', firstOutfit.outfitName);
            console.log('- Shirt:', firstOutfit.shirt?.color, firstOutfit.shirt?.hex);
            console.log('- Pants:', firstOutfit.pants?.color, firstOutfit.pants?.hex);
            console.log('- Reason:', firstOutfit.overallReason);
          }
          
        } else {
          console.log('❌ Color recommendations failed:', recommendationsData.message);
        }
        
      } else {
        console.log('❌ Face analysis failed:', analysisData.message);
      }
      
    } catch (analysisError) {
      console.error('❌ Analysis error:', analysisError.message);
    }
    
    // Test direct analysis as fallback
    console.log('\n4. Testing direct analysis (fallback)...');
    try {
      const directAnalysisResponse = await fetch(`${API_BASE_URL}/face/analyze-direct`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          imageUrl: testImageUrl,
          publicId: 'test-face-123'
        })
      });
      
      console.log('Direct analysis response status:', directAnalysisResponse.status);
      const directAnalysisData = await directAnalysisResponse.json();
      console.log('Direct analysis response:', directAnalysisData);
      
      if (directAnalysisData.success) {
        console.log('✅ Direct analysis also working');
      } else {
        console.log('❌ Direct analysis failed:', directAnalysisData.message);
      }
      
    } catch (directError) {
      console.error('❌ Direct analysis error:', directError.message);
    }
    
    // Summary
    console.log('\n📋 Face Analysis Flow Summary:');
    console.log('- Login: ✅ Working');
    console.log('- URL-based Analysis: Test above');
    console.log('- Color Recommendations: Test above');
    console.log('- Direct Analysis: Test above');
    console.log('\nNext: Test in browser with actual uploaded image');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

testFaceAnalysisFlow().catch(console.error);
