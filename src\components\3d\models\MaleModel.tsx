'use client';

import React, { useRef, useEffect } from 'react';
import { useGLTF } from '@react-three/drei';
import { Color } from 'three';
import * as THREE from 'three';

interface MaleModelProps {
  colorCombination?: {
    shirt: string;
    pants: string;
    shoes: string;
  } | null;
  enableColorSync?: boolean;
}

export const MaleModel: React.FC<MaleModelProps> = ({ 
  colorCombination, 
  enableColorSync = false 
}) => {
  const group = useRef<THREE.Group>(null);
  const { nodes, materials } = useGLTF('/male.glb') as any;

  // Intelligent color assignment based on your specific model parts
  const getIntelligentColor = (materialName: string, index: number, meshName: string = ''): Color => {
    const safeMaterialName = materialName || '';
    const safeMeshName = meshName || '';
    const name = safeMaterialName.toLowerCase();
    const mesh = safeMeshName.toLowerCase();

    console.log(`🎨 Assigning color for material: "${safeMaterialName}" on mesh: "${safeMeshName}"`);

    // Use color combination if available
    if (colorCombination && enableColorSync) {
      // Apply shirt color to sweater/shirt meshes
      if (mesh.includes('ch31_sweater') || mesh.includes('sweater') ||
          name.includes('ch31_sweater') || name.includes('sweater') ||
          mesh.includes('shirt') || name.includes('shirt')) {
        console.log('   → Applied shirt color from combination:', colorCombination.shirt);
        return new Color(colorCombination.shirt);
      }

      // Apply pants color to pants meshes
      if (mesh.includes('ch31_pants') || mesh.includes('pants') ||
          name.includes('ch31_pants') || name.includes('pants')) {
        console.log('   → Applied pants color from combination:', colorCombination.pants);
        return new Color(colorCombination.pants);
      }

      // Apply shoes color to shoes meshes
      if (mesh.includes('ch31_shoes') || mesh.includes('shoes') ||
          name.includes('ch31_shoes') || name.includes('shoes')) {
        console.log('   → Applied shoes color from combination:', colorCombination.shoes);
        return new Color(colorCombination.shoes);
      }
    }

    // Default colors for body parts (not affected by color combinations)
    if (mesh.includes('ch31_body') || mesh.includes('body')) {
      console.log('   → Applied skin tone for body mesh');
      return new Color(0xfdbcb4); // Natural skin tone
    }
    if (mesh.includes('ch31_hair') || mesh.includes('hair')) {
      console.log('   → Applied brown color for hair mesh');
      return new Color(0x4a2c17); // Dark brown hair
    }
    if (mesh.includes('ch31_eyelashes') || mesh.includes('eyelash')) {
      console.log('   → Applied dark color for eyelashes mesh');
      return new Color(0x2c1810); // Very dark brown for eyelashes
    }
    if (mesh.includes('ch31_collar') || mesh.includes('collar')) {
      console.log('   → Applied white color for collar mesh');
      return new Color(0xffffff); // Pure white collar
    }

    // Fallback colors when no color combination is available
    if (mesh.includes('ch31_pants') || mesh.includes('pants')) {
      console.log('   → Applied default blue color for pants mesh');
      return new Color(0x1e40af); // Blue jeans color
    }
    if (mesh.includes('ch31_shoes') || mesh.includes('shoes')) {
      console.log('   → Applied default dark color for shoes mesh');
      return new Color(0x1f2937); // Dark shoe color
    }
    if (mesh.includes('ch31_sweater') || mesh.includes('sweater')) {
      console.log('   → Applied default red color for sweater mesh');
      return new Color(0xe11d48); // Vibrant red sweater
    }

    // Then check material name
    if (name.includes('ch31_body') || name.includes('body')) {
      console.log('   → Applied skin tone for body material');
      return new Color(0xfdbcb4); // Natural skin tone
    }
    if (name.includes('ch31_hair') || name.includes('hair')) {
      console.log('   → Applied brown color for hair material');
      return new Color(0x4a2c17); // Dark brown hair
    }
    if (name.includes('ch31_eyelashes') || name.includes('eyelash')) {
      console.log('   → Applied dark color for eyelashes material');
      return new Color(0x2c1810); // Very dark brown for eyelashes
    }
    if (name.includes('ch31_pants') || name.includes('pants')) {
      console.log('   → Applied blue color for pants material');
      return new Color(0x1e40af); // Blue jeans color
    }
    if (name.includes('ch31_shoes') || name.includes('shoes')) {
      console.log('   → Applied dark color for shoes material');
      return new Color(0x1f2937); // Dark shoe color
    }
    if (name.includes('ch31_sweater') || name.includes('sweater')) {
      console.log('   → Applied red color for sweater material');
      return new Color(0xdc2626); // Red sweater
    }
    if (name.includes('ch31_collar') || name.includes('collar')) {
      console.log('   → Applied white color for collar material');
      return new Color(0xf8fafc); // White collar
    }

    // General fallback colors
    if (name.includes('skin') || name.includes('body') || name.includes('face')) {
      return new Color(0xfdbcb4); // Natural skin tone
    }
    if (name.includes('hair')) {
      return new Color(0x4a2c17); // Dark brown hair
    }
    if (name.includes('eye')) {
      return new Color(0x2e4057); // Dark blue eyes
    }
    if (name.includes('cloth') || name.includes('shirt') || name.includes('dress')) {
      return new Color(0x1e3a8a); // Navy blue clothing
    }
    if (name.includes('pants') || name.includes('trouser')) {
      return new Color(0x374151); // Dark gray pants
    }
    if (name.includes('shoe') || name.includes('boot')) {
      return new Color(0x1f2937); // Dark shoe color
    }
    if (name.includes('metal') || name.includes('button')) {
      return new Color(0x9ca3af); // Metallic gray
    }
    if (name.includes('leather') || name.includes('belt')) {
      return new Color(0x92400e); // Brown leather
    }

    // Generate consistent color based on material name hash
    let hash = 0;
    const hashString = safeMaterialName || `material_${index}`;
    for (let i = 0; i < hashString.length; i++) {
      hash = ((hash << 5) - hash + hashString.charCodeAt(i)) & 0xffffffff;
    }

    // Generate pleasant colors (avoid pure black/white)
    const hue = Math.abs(hash) % 360;
    const saturation = 40 + (Math.abs(hash >> 8) % 40); // 40-80%
    const lightness = 30 + (Math.abs(hash >> 16) % 40); // 30-70%

    return new Color().setHSL(hue / 360, saturation / 100, lightness / 100);
  };

  // Apply colors to materials when color combination changes
  useEffect(() => {
    if (!materials) return;

    console.log('🔄 Updating male model colors...');
    console.log('Color combination:', colorCombination);
    console.log('Enable color sync:', enableColorSync);

    let updatedMaterials = 0;

    // Apply colors to all materials
    Object.entries(materials).forEach(([materialName, material], index) => {
      if (material && typeof material === 'object' && 'color' in material) {
        const mat = material as THREE.MeshStandardMaterial;
        const newColor = getIntelligentColor(materialName, index, '');
        
        if (mat.color && !mat.color.equals(newColor)) {
          mat.color.copy(newColor);
          mat.needsUpdate = true;
          updatedMaterials++;
        }
      }
    });

    console.log(`✅ Updated ${updatedMaterials} materials on male model`);
  }, [colorCombination, enableColorSync, materials]);

  if (!nodes || !materials) {
    console.log('⏳ Male model not loaded yet...');
    return null;
  }

  return (
    <group ref={group} dispose={null} scale={[1, 1, 1]} position={[0, -1, 0]}>
      {Object.entries(nodes).map(([nodeName, node]) => {
        if (node && typeof node === 'object' && 'geometry' in node && 'material' in node) {
          const mesh = node as THREE.Mesh;
          return (
            <mesh
              key={nodeName}
              castShadow
              receiveShadow
              geometry={mesh.geometry}
              material={mesh.material}
              position={mesh.position}
              rotation={mesh.rotation}
              scale={mesh.scale}
            />
          );
        }
        return null;
      })}
    </group>
  );
};

// Preload the model
useGLTF.preload('/male.glb');
