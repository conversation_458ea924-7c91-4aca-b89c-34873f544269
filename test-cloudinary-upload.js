// Test Cloudinary upload functionality
const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';

async function testCloudinaryUpload() {
  console.log('Testing Cloudinary Upload...');
  
  // First, login to get a token
  try {
    console.log('\n1. Logging in...');
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'TestPass123'
      })
    });
    
    const loginData = await loginResponse.json();
    
    if (!loginData.success || !loginData.token) {
      console.error('❌ Login failed');
      return;
    }
    
    const token = loginData.token;
    console.log('✅ Login successful');
    
    // Test upload signature
    console.log('\n2. Getting upload signature...');
    const signatureResponse = await fetch(`${API_BASE_URL}/upload/mobile-signature`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    
    const signatureData = await signatureResponse.json();
    console.log('Signature response:', signatureData);
    
    if (signatureData.success && signatureData.signature) {
      console.log('✅ Upload signature obtained');
      
      // Test direct upload to Cloudinary (without actual file)
      console.log('\n3. Testing Cloudinary upload URL...');
      const uploadUrl = signatureData.upload_url || 'https://api.cloudinary.com/v1_1/dy1tsskkm/image/upload';
      
      // Create a simple test form data (this will fail but shows the endpoint is reachable)
      const testFormData = new FormData();
      testFormData.append('signature', signatureData.signature);
      testFormData.append('timestamp', signatureData.timestamp.toString());
      testFormData.append('api_key', signatureData.api_key);
      testFormData.append('folder', signatureData.folder);
      
      try {
        const uploadResponse = await fetch(uploadUrl, {
          method: 'POST',
          body: testFormData,
        });
        
        console.log('Upload test response status:', uploadResponse.status);
        const uploadResult = await uploadResponse.text();
        console.log('Upload test response:', uploadResult);
        
        if (uploadResponse.status === 400) {
          console.log('✅ Cloudinary endpoint is reachable (400 expected without file)');
        }
      } catch (uploadError) {
        console.error('❌ Cloudinary upload test failed:', uploadError.message);
      }
      
    } else {
      console.error('❌ Failed to get upload signature');
    }
    
    // Test unsigned upload (if available)
    console.log('\n4. Testing unsigned upload...');
    const cloudName = 'dy1tsskkm';
    const unsignedUploadUrl = `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`;
    
    const unsignedFormData = new FormData();
    unsignedFormData.append('upload_preset', 'face-app-unsigned'); // This might not exist
    unsignedFormData.append('folder', 'faceapp-uploads');
    
    try {
      const unsignedResponse = await fetch(unsignedUploadUrl, {
        method: 'POST',
        body: unsignedFormData,
      });
      
      console.log('Unsigned upload test status:', unsignedResponse.status);
      const unsignedResult = await unsignedResponse.text();
      console.log('Unsigned upload test response:', unsignedResult);
      
      if (unsignedResponse.status === 400) {
        const errorData = JSON.parse(unsignedResult);
        if (errorData.error && errorData.error.message.includes('upload_preset')) {
          console.log('ℹ️  Unsigned upload preset not configured (this is normal)');
        }
      }
    } catch (unsignedError) {
      console.error('Unsigned upload test error:', unsignedError.message);
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

testCloudinaryUpload().catch(console.error);
